package com.databahn.planes.dataplane.service;

import com.databahn.planes.constants.ChangeFlagConstants;
import com.databahn.planes.exception.ValidationException;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ChangeFlagService {

  private final KafkaTemplate<String, String> kafkaTemplate;
  private final ObjectMapper objectMapper;

  @Autowired
  public ChangeFlagService(
      ObjectMapper objectMapper,
      @Qualifier("inputKafkaTemplate") KafkaTemplate<String, String> kafkaTemplate) {
    this.kafkaTemplate = kafkaTemplate;
    this.objectMapper = objectMapper;
  }

  public void sendSync(List<ChangeFlag> changeFlags) {
    for (ChangeFlag flagMessage : changeFlags) {
      try {
        String key = flagMessage.entityId();
        String value = objectMapper.writeValueAsString(flagMessage.body());
        ProducerRecord<String, String> message = getProducerRecord(flagMessage, key, value);
        CompletableFuture<SendResult<String, String>> result = kafkaTemplate.send(message);
        result.get();
      } catch (InterruptedException | ExecutionException e) {
        log.error("failed to send to kafka", e);
        throw new RuntimeException(e);
      } catch (JsonProcessingException e) {
        log.error("failed to parse change flag entity" + flagMessage, e);
        throw new ValidationException("Invalid change flag entity", e);
      }
    }
  }

  private static ProducerRecord<String, String> getProducerRecord(
      ChangeFlag flagMessage, String key, String value) {
    List<Header> headers =
        List.of(
            new RecordHeader(ChangeFlagConstants.HeaderAction, flagMessage.action().getBytes()),
            new RecordHeader(ChangeFlagConstants.HeaderTenantId, flagMessage.tenantId().getBytes()),
            new RecordHeader(
                ChangeFlagConstants.HeaderRequestId, flagMessage.requestId().getBytes()),
            new RecordHeader(ChangeFlagConstants.HeaderEntityType, flagMessage.type().getBytes()),
            new RecordHeader(
                ChangeFlagConstants.HeaderSchemaVersion, flagMessage.schemaVersion().getBytes()));
    return new ProducerRecord<>(ChangeFlagConstants.KafkaTopic, null, key, value, headers);
  }
}
