package com.databahn.planes.dataplane.config;

import feign.Client;
import feign.codec.Encoder;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.Proxy;
import java.net.URL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.security.OAuth2AccessTokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

@Configuration
@Slf4j
public class ControlPlaneClientConfig {

  @Value("${HTTPS_PROXY:#{null}}")
  private String httpsProxy;

  @Bean("feignProxyClient")
  public Client feignProxyClient() throws MalformedURLException {
    if (!StringUtils.isEmpty(httpsProxy)) {
      URL url = new URL(httpsProxy);
      String host = url.getHost();
      int port = url.getPort();
      log.info(
          "Control plane feign client configured with proxy host: {} and port: {}", host, port);
      return new Client.Proxied(
          null, null, new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port)));
    } else {
      log.info("Control plane feign client configured without proxy");
      return new Client.Default(null, null);
    }
  }

  @Bean
  @ConditionalOnBean({OAuth2AuthorizedClientService.class, ClientRegistrationRepository.class})
  @ConditionalOnMissingBean
  public OAuth2AuthorizedClientManager feignOAuth2AuthorizedClientManager(
      ClientRegistrationRepository clientRegistrationRepository,
      OAuth2AuthorizedClientService oAuth2AuthorizedClientService) {
    return SecurityCommonUtils.builFeignOAuth2AuthorizedClientManager(
        clientRegistrationRepository, oAuth2AuthorizedClientService, httpsProxy);
  }

  @Bean
  @ConditionalOnBean({OAuth2AuthorizedClientManager.class})
  public OAuth2AccessTokenInterceptor defaultOAuth2AccessTokenInterceptor(
      @Value("${spring.cloud.openfeign.oauth2.clientRegistrationId:}") String clientRegistrationId,
      OAuth2AuthorizedClientManager oAuth2AuthorizedClientManager) {
    return new OAuth2AccessTokenInterceptor(clientRegistrationId, oAuth2AuthorizedClientManager);
  }


}
