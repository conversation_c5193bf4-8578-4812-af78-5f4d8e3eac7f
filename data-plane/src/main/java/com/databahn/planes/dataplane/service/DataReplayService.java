package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.constants.DataReplayConstants;
import com.databahn.planes.dataplane.model.JobRequest;
import com.databahn.planes.exception.ValidationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataReplayService {

  private final KafkaTemplate<String, String> kafkaTemplate;
  private final ObjectMapper objectMapper;

  @Autowired
  public DataReplayService(
      @Qualifier("processingKafkaTemplate") KafkaTemplate<String, String> kafkaTemplate,
      ObjectMapper objectMapper) {
    this.kafkaTemplate = kafkaTemplate;
    this.objectMapper = objectMapper;
  }

  public void sendSync(JobRequest jobRequest) {
    try {
      String key = jobRequest.getId();
      String value = objectMapper.writeValueAsString(jobRequest);
      List<Header> headers =
          List.of(
              new RecordHeader(
                  DataReplayConstants.HEADER_KEY, DataReplayConstants.SOURCE.getBytes()));
      ProducerRecord<String, String> message =
          new ProducerRecord<>(DataReplayConstants.KAFKA_TOPIC, null, key, value, headers);
      CompletableFuture<SendResult<String, String>> result = kafkaTemplate.send(message);
      result.get();
    } catch (InterruptedException | ExecutionException e) {
      log.error("failed to send to kafka", e);
      throw new RuntimeException(e);
    } catch (JsonProcessingException e) {
      log.error("failed to parse replay flag entity" + jobRequest.getId(), e);
      throw new ValidationException("Invalid change data replay entity", e);
    }
  }
}
