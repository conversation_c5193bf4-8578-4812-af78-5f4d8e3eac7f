package com.databahn.planes.dataplane.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Service;

@Service
public class SensitiveCache {
  private final Cache<String, CacheObject> cache;

  public SensitiveCache() {
    this.cache =
        CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();
  }

  public void put(String key, Map<String, String> sensitiveData) {
    cache.put(key, new CacheObject(sensitiveData));
  }

  public Optional<Map<String, String>> get(String key) {
    return Optional.ofNullable(cache.getIfPresent(key))
        .map(cacheObject -> cacheObject.sensitiveData);
  }

  @Override
  public String toString() {
    return "*******";
  }

  public static class CacheObject {
    private final Map<String, String> sensitiveData;

    public CacheObject(Map<String, String> sensitiveData) {
      this.sensitiveData = sensitiveData;
    }

    @Override
    public String toString() {
      return "*******";
    }
  }
}
