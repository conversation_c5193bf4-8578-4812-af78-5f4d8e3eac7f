package com.databahn.planes.dataplane.service.admin;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.dataplane.service.KSqlDbService;
import com.databahn.planes.model.changeflag.AdminChangeFlag;
import com.databahn.planes.model.changeflag.KsqlOperations;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class KSqlDbRequestHandler implements AdminRequestHandler {

  private KSqlDbService kSqlDbService;

  @Override
  public boolean handleAdminRequest(ChangeFlagAction action, AdminChangeFlag adminChangeFlag) {
    KsqlOperations ksqlOperations = adminChangeFlag.getKsqlOperations();
    if (ksqlOperations == null || !ksqlOperations.hasQueries()) {
      log.error("No queries found in ksql operations");
    }
    List<KsqlOperations.KsqlQuery> queries = ksqlOperations.getQueries();
    for (KsqlOperations.KsqlQuery query : queries) {
      kSqlDbService.execute(query.getQuery());
      log.info("Executed ksqldb query: {}", query.getQuery());
    }
    return true;
  }
}
