package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.model.Payload;
import com.google.common.collect.Lists;
import io.confluent.ksql.api.client.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class KSqlDbService {

  private Client kSqlDbClient;

  @SneakyThrows
  public Payload executeAndReadResult(String query) {
    BatchedQueryResult batchedQueryResult = kSqlDbClient.executeQuery(query);
    List<Row> rows = batchedQueryResult.get();
    if (rows == null || rows.size() == 0) {
      return new Payload(Lists.newArrayList(), Lists.newArrayList());
    }
    List<List<Object>> result = Lists.newArrayList();
    List<String> columnNames = rows.get(0).columnNames();
    for (Row row : rows) {
      result.add(Lists.newArrayList(row.values().getList()));
    }
    return new Payload(columnNames, result);
  }

  @SneakyThrows
  public void execute(String query) {
    CompletableFuture<ExecuteStatementResult> result = kSqlDbClient.executeStatement(query);
    result.get();
  }

  @SneakyThrows
  public List<String> tables() {
    CompletableFuture<List<TableInfo>> result = kSqlDbClient.listTables();
    List<TableInfo> tableInfos = result.get();
    return tableInfos.stream().map(TableInfo::getName).collect(Collectors.toList());
  }

  @SneakyThrows
  public List<String> streams() {
    CompletableFuture<List<StreamInfo>> result = kSqlDbClient.listStreams();
    List<StreamInfo> streamInfos = result.get();
    return streamInfos.stream().map(StreamInfo::getName).collect(Collectors.toList());
  }

  public String status() {
    try {
      CompletableFuture<ServerInfo> result = kSqlDbClient.serverInfo();
      ServerInfo serverInfo = result.get();
      if (serverInfo != null) {
        return "UP";
      }
      return "DOWN";
    } catch (Exception e) {
      return "DOWN";
    }
  }
}
