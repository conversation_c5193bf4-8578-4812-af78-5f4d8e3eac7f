package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.dataplane.model.Payload;
import com.databahn.planes.model.checkpoint.CheckpointsRequest;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpoint;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpointRequest;
import com.databahn.planes.model.checkpoint.MigrationStatus;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import com.databahn.planes.response.Response;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CloudSourceCheckpointService {

  private final ControlPlaneClient client;
  private final KSqlDbService kSqlDbService;
  private final DataPlaneConfig dataPlaneConfig;

  private List<List<String>> SOURCE_ID_PREFIXES = new ArrayList<>();

  public CloudSourceCheckpointService(
      ControlPlaneClient client, KSqlDbService kSqlDbService, DataPlaneConfig dataPlaneConfig) {
    this.client = client;
    this.kSqlDbService = kSqlDbService;
    this.dataPlaneConfig = dataPlaneConfig;

    SOURCE_ID_PREFIXES.add(List.of("0", "1", "2", "3", "4"));
    SOURCE_ID_PREFIXES.add(List.of("5", "6", "7", "8", "9"));
    SOURCE_ID_PREFIXES.add(List.of("a", "b", "c", "d", "e", "f"));
  }

  public Optional<CloudSourceCheckpoint> get(UUID tenantId, UUID sourceId) {
    Response<CloudSourceCheckpoint> cloudSourceCheckpointResponse =
        client.getCloudSourceCheckpoint(tenantId, sourceId);
    return Optional.ofNullable(cloudSourceCheckpointResponse.getData());
  }

  public void saveCheckpoint(CloudSourceCheckpointRequest checkpoint) {
    validateCheckpoint(checkpoint);
    client.saveCloudSourceCheckpoint(checkpoint);
  }

  public List<CloudSourceCheckpoint> get(CheckpointsRequest request) {
    Response<List<CloudSourceCheckpoint>> checkpoints = client.getCloudSourceCheckpoints(request);
    if (checkpoints.getData() == null) {
      log.info("no checkpoints found for request: {}", request);
      return new ArrayList<>();
    }
    return checkpoints.getData();
  }

  public MigrationStatus migrate(boolean addOnly) {
    List<TenantDataPlane> tenantDataPlanes = dataPlaneConfig.getTenantDataPlanes();
    int migrationSuccessCount = 0;
    int migrationErrorCount = 0;
    List<String> errors = new ArrayList<>();
    for (TenantDataPlane tenantDataPlane : tenantDataPlanes) {
      UUID tenantId = tenantDataPlane.getTenantId();
      UUID dataPlaneId = tenantDataPlane.getDataPlaneId();
      int migrationCount = 0;
      log.info("Migrating checkpoints for tenantId: {} and dataPlaneId: {}", tenantId, dataPlaneId);

      for (List<String> sourceIdPrefixes : SOURCE_ID_PREFIXES) {
        String condition =
            sourceIdPrefixes.stream()
                .map(
                    prefix ->
                        String.format(
                            "(TENANT_SOURCE_ID like '%s:%s%%')=true",
                            tenantDataPlane.getTenantId(), prefix))
                .collect(Collectors.joining(" OR "));
        String query =
            "select TENANT_SOURCE_ID, CHECK_POINT_VALUE, CHECK_POINT_DATE from CLOUD_SOURCE_CHECKPOINTS where ("
                + condition
                + ");";
        Payload result = kSqlDbService.executeAndReadResult(query);
        if (result.rows().isEmpty()) {
          continue;
        }
        Iterator<List<Object>> dataIterator = result.rows().iterator();

        while (dataIterator.hasNext()) {
          try {
            migrationCount++;
            saveCheckpoint(tenantId, dataPlaneId, dataIterator, addOnly);
            migrationSuccessCount++;
          } catch (Exception e) {
            log.error(
                "Error migrating checkpoint for tenantId: {} and dataPlaneId: {}",
                tenantId,
                dataPlaneId,
                e);
            String rootCauseMessage = ExceptionUtils.getRootCauseMessage(e);
            errors.add(
                String.format(
                    "Error migrating checkpoint for tenantId: %s and dataPlaneId: %s. Error: %s",
                    tenantId, dataPlaneId, rootCauseMessage));
            migrationErrorCount++;
          }
        }
      }
      log.info(
          "Migrated cloud source {} checkpoints for tenantId: {} and dataPlaneId: {}",
          migrationCount,
          tenantDataPlane.getTenantId(),
          tenantDataPlane.getDataPlaneId());
    }
    log.info(
        "Migration completed. Migrated {} checkpoints successfully and {} checkpoints failed",
        migrationSuccessCount,
        migrationErrorCount);
    return new MigrationStatus(migrationSuccessCount, errors);
  }

  private void saveCheckpoint(
      UUID tenantId, UUID dataPlaneId, Iterator<List<Object>> dataIterator, boolean addOnly) {
    List<Object> data = dataIterator.next();
    CloudSourceCheckpointRequest checkpoint = new CloudSourceCheckpointRequest();
    checkpoint.setTenantId(tenantId);
    checkpoint.setDataPlaneId(dataPlaneId);
    String tenantSourceId = (String) data.get(0);
    String[] split = tenantSourceId.split(":");
    if (split.length != 2) {
      log.error("Invalid tenant source id: {}", tenantSourceId);
      throw new IllegalArgumentException("Invalid tenant source id: " + tenantSourceId);
    }
    String ksqlDbSourceId = split[1];
    String checkpointValue = (String) data.get(1);
    Integer checkpointTime = (Integer) data.get(2);
    checkpoint.setSourceId(UUID.fromString(ksqlDbSourceId));
    checkpoint.setCheckpoint(checkpointValue);
    checkpoint.setTimestamp(checkpointTime.longValue() * 1000);
    checkpoint.setAddOnly(addOnly);
    client.saveCloudSourceCheckpoint(checkpoint);
  }

  private static void validateCheckpoint(CloudSourceCheckpoint checkpoint) {
    if (checkpoint == null) {
      throw new IllegalArgumentException("Checkpoint cannot be null");
    }
    if (checkpoint.getTenantId() == null) {
      throw new IllegalArgumentException("TenantId cannot be null");
    }
    if (checkpoint.getSourceId() == null) {
      throw new IllegalArgumentException("SourceId cannot be null");
    }
    if (checkpoint.getDataPlaneId() == null) {
      throw new IllegalArgumentException("DataPlaneId cannot be null");
    }
    if (checkpoint.getCheckpoint() == null || checkpoint.getCheckpoint().isEmpty()) {
      throw new IllegalArgumentException("Checkpoint cannot be null or empty");
    }
    if (checkpoint.getTimestamp() == null) {
      throw new IllegalArgumentException("Timestamp cannot be null");
    }
  }
}
