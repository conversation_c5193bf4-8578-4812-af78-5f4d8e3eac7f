package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.service.kafkaconnet.KafkaConnectorChangeFlagProcessingService;
import com.databahn.planes.model.changeflag.ChangeFlag;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ConnectorChangeFlagProcessingService {

  private final KafkaConnectorChangeFlagProcessingService connectorCfProcessingService;

  public void processChangeFlag(List<ChangeFlag> cfs) {
    cfs.forEach(
        cf -> {
          log.debug("Handling change flag: {}", cf);

          String action = cf.action();

          if (Objects.nonNull(action)
              && ("add".equals(action) || "update".equals(action) || "delete".equals(action))) {
            Map<String, Object> body = cf.body();

            if (Objects.nonNull(body)) {
              Object entityObject = body.get("entity");

              if (entityObject instanceof Map<?, ?>) {
                Map<String, Object> entity = (Map<String, Object>) entityObject;
                Object configObject = entity.get("config");

                if (Objects.nonNull(configObject) && configObject instanceof Map<?, ?>) {
                  Map<String, Object> config = (Map<String, Object>) configObject;
                  // ADDED CODE FOR DYNAMIC TOPIC CREATION FOR KAFKA CONNECT SOURCE CONNECTOR/SINK
                  // DISPENSER
                  if (connectorCfProcessingService.processChangeFlagForConnector(
                      cf, action, entity)) {
                    log.error("Kafka Connector CF Failed  in change flag {}.", cf);
                  }
                }
              }
            }
          }
        });
  }
}
