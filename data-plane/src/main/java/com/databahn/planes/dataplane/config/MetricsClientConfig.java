package com.databahn.planes.dataplane.config;

import feign.codec.Encoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign configuration specifically for metrics endpoints that require octet-stream encoding.
 * This configuration is only applied to clients that explicitly reference it.
 */
@Configuration
public class MetricsClientConfig {

  /**
   * Encoder specifically for metrics endpoints that need to send raw string data
   * as application/octet-stream.
   */
  @Bean
  public Encoder metricsEncoder() {
    return new MetricsOctetStreamEncoder(new feign.codec.Encoder.Default());
  }
}
