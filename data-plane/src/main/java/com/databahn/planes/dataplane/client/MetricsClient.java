package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.config.ControlPlaneClientConfig;
import com.databahn.planes.dataplane.config.MetricsFeignConfig;
import com.databahn.planes.response.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(
    value = "metrics-client",
    url = "${controller.baseurl}",
    configuration = {ControlPlaneClientConfig.class, MetricsFeignConfig.class})
public interface MetricsClient {

  @RequestMapping(
      method = RequestMethod.POST,
      value = "/v1/metrics/{type}",
      consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  Response<Object> saveMetrics(@PathVariable("type") String metricType, @RequestBody String stats);
}
