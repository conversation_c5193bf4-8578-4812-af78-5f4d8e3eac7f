package com.databahn.planes.dataplane.config;

import feign.RequestTemplate;
import feign.codec.EncodeException;
import feign.codec.Encoder;
import java.lang.reflect.Type;
import java.util.regex.Pattern;
import org.springframework.http.MediaType;

/**
 * Specialized encoder for metrics endpoints that need to send raw string data
 * as application/octet-stream. This encoder is more robust and handles edge cases properly.
 */
public class MetricsOctetStreamEncoder implements Encoder {

  private static final Pattern METRICS_URL_PATTERN = Pattern.compile(".*/v1/metrics/[^/]+/?$");
  private final Encoder defaultEncoder;

  public MetricsOctetStreamEncoder(Encoder defaultEncoder) {
    this.defaultEncoder = defaultEncoder;
  }

  @Override
  public void encode(Object object, Type bodyType, RequestTemplate template)
      throws EncodeException {
    
    if (object == null) {
      throw new EncodeException("Cannot encode null object");
    }

    try {
      String url = template.url();
      
      // Only apply octet-stream encoding for specific metrics endpoints with String data
      if (isMetricsEndpoint(url) && object instanceof String) {
        String content = (String) object;
        
        // Validate that we have actual content to send
        if (content.trim().isEmpty()) {
          throw new EncodeException("Cannot send empty metrics data");
        }
        
        // Set the content type and body for octet-stream
        template.header("Content-Type", MediaType.APPLICATION_OCTET_STREAM_VALUE);
        template.body(content);
        
      } else {
        // For all other cases, delegate to the default encoder
        defaultEncoder.encode(object, bodyType, template);
      }
      
    } catch (Exception e) {
      if (e instanceof EncodeException) {
        throw e;
      }
      throw new EncodeException(
          "Failed to encode object of type: " + object.getClass().getSimpleName() + 
          " for URL: " + template.url(), e);
    }
  }

  /**
   * Checks if the URL is a metrics endpoint that should use octet-stream encoding.
   * Uses regex pattern matching for more robust URL validation.
   */
  private boolean isMetricsEndpoint(String url) {
    if (url == null || url.trim().isEmpty()) {
      return false;
    }
    
    // Remove query parameters and fragments for cleaner matching
    String cleanUrl = url.split("\\?")[0].split("#")[0];
    
    return METRICS_URL_PATTERN.matcher(cleanUrl).matches();
  }
}
