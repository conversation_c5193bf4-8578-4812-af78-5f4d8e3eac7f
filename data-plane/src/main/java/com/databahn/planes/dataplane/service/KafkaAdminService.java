package com.databahn.planes.dataplane.service;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.constants.EntityType;
import com.databahn.planes.dataplane.constants.KafkaConnectConstants;
import com.databahn.planes.dataplane.processing.AckProducer;
import com.databahn.planes.dataplane.service.admin.AdminService;
import com.databahn.planes.dataplane.service.kafkaconnet.KafkaConnectorChangeFlagProcessingService;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.databahn.planes.model.changeflag.KsqlOperations;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class KafkaAdminService {

  private final KafkaAdmin adminClient;
  private final KafkaConnectorChangeFlagProcessingService connectorCfProcessingService;
  private final ObjectMapper objectMapper;
  private final KSqlDbService kSqlDbService;
  private final AckProducer ackProducer;
  private final AdminService adminService;

  public KafkaAdminService(
      @Qualifier("processingKafkaAdmin") KafkaAdmin adminClient,
      KafkaConnectorChangeFlagProcessingService connectorCfProcessingService,
      ObjectMapper objectMapper,
      KSqlDbService kSqlDbService,
      AckProducer ackProducer,
      AdminService adminService) {
    this.adminClient = adminClient;
    this.connectorCfProcessingService = connectorCfProcessingService;
    this.objectMapper = objectMapper;
    this.kSqlDbService = kSqlDbService;
    this.ackProducer = ackProducer;
    this.adminService = adminService;
  }

  public void processChangeFlag(List<ChangeFlag> cfs) {
    List<ChangeFlag> ignoreList = new ArrayList<>();
    for (ChangeFlag cf : cfs) {
      log.debug("Handling change flag: {}", cf);
      String action = cf.action();
      if (Objects.nonNull(action)) {
        Map<String, Object> body = cf.body();
        if (Objects.nonNull(body)) {
          Object entityObject = body.get("entity");

          if (entityObject instanceof Map<?, ?>) {
            Map<String, Object> entity = (Map<String, Object>) entityObject;

            if (cf.type() != null
                && (cf.type().startsWith("destination_")
                    || EntityType.SOURCE.getChangeFlagType().equals(cf.type()))) {
              Object configObject = entity.get("config");
              if (configObject == null) {
                log.error("config missing in source or destination change flag {}", cf.requestId());
                continue;
              }
              Map<String, Object> config = (Map<String, Object>) configObject;
              if (!ChangeFlagAction.DELETE.getAction().equals(action)) {
                Optional.ofNullable(config.get("is_throttled"))
                    .ifPresent(
                        isThrottled -> {
                          if ((isThrottled instanceof Boolean && (Boolean) isThrottled)
                              || (isThrottled instanceof String
                                  && Boolean.parseBoolean((String) isThrottled))
                              || (isThrottled instanceof byte[]
                                  && Boolean.parseBoolean(new String((byte[]) isThrottled)))) {

                            String entityId = entity.get("id").toString();
                            log.info(
                                "Throttling is enabled. Creating topic for entity - {}", entityId);
                            NewTopic topic =
                                new NewTopic(
                                    "db.staging.throttled.destination." + entityId, 4, (short) 3);
                            adminClient.createOrModifyTopics(topic);
                          } else {
                            log.error("Invalid value for is_throttled in change flag {}.", cf);
                          }
                        });
              }
              // ADDED CODE FOR DYNAMIC TOPIC CREATION FOR KAFKA CONNECT SOURCE CONNECTOR/SINK
              // DISPENSER
              if (connectorCfProcessingService.processChangeFlagForConnector(cf, action, entity)) {
                ignoreList.add(cf);
                log.error("Kafka Connector CF Failed  in change flag {}.", cf);
              }
            } else if (EntityType.VC_RULE.getChangeFlagType().equals(cf.type())) {
              Object aggregationKsqlOperations = entity.get("aggregation_ksql_operations");
              if (aggregationKsqlOperations != null) {
                try {
                  KsqlOperations ksqlOperations =
                      objectMapper.convertValue(aggregationKsqlOperations, KsqlOperations.class);
                  List<KsqlOperations.KsqlQuery> statements = ksqlOperations.getQueries();
                  if (!CollectionUtils.isEmpty(statements)) {
                    for (KsqlOperations.KsqlQuery statement : statements) {
                      String query = statement.getQuery();
                      kSqlDbService.execute(query);
                      log.info("Executed ksqldb query for agg rule: {}", query);
                    }
                    sendAck(cf, KafkaConnectConstants.ACK_STATUS_SUCCESS, "DpKSql", null);
                  }
                } catch (Exception e) {
                  log.error(
                      "Failed to execute KSQL query for agg rule change flag for request "
                          + cf.requestId()
                          + " entity "
                          + cf.entityId(),
                      e);
                  sendAck(
                      cf,
                      KafkaConnectConstants.ACK_STATUS_FAILURE,
                      "DpKSql",
                      ExceptionUtils.getRootCauseMessage(e));
                }
              }
            } else if (EntityType.ADMIN.getChangeFlagType().equals(cf.type())) {
              try {
                boolean ignore = adminService.handleAdminChangeFlag(cf);
                if (ignore) {
                  ignoreList.add(cf);
                }
                sendAck(cf, KafkaConnectConstants.ACK_STATUS_SUCCESS, "DpAdmin", null);
              } catch (Exception e) {
                log.error("Failed to execute Admin change flag for request " + cf.requestId(), e);
                sendAck(
                    cf,
                    KafkaConnectConstants.ACK_STATUS_FAILURE,
                    "DpAdmin",
                    ExceptionUtils.getRootCauseMessage(e));
              }
            }
          } else {
            log.error("Invalid entity body for request {}", cf.requestId());
          }
        } else {
          log.error("Invalid change flag body for request {}", cf.requestId());
        }
      }
    }

    cfs.removeAll(ignoreList);
  }

  public void sendAck(ChangeFlag cf, String status, String service, String error) {
    Ack ack = ackProducer.buildAck(cf, status, service, error);
    ackProducer.sendToKafkaSync(ack);
  }
}
